package com.swhd.agent.web.tenant.account.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeDetailClient;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeBySourceIdParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeByTitleParam;
import com.swhd.agent.web.tenant.account.mq.consumer.AccountOceanengineContentDownloadConsumer;
import com.swhd.agent.web.tenant.account.service.AccountOceanengineCustomerRechargeDetailService;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeDetailResultVo;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 自助充值明细表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerRechargeDetail")
public class AccountOceanengineCustomerRechargeDetailController {

    private final DownloadExportRecordClient downloadExportRecordClient;
    private final AccountOceanengineCustomerRechargeDetailClient accountOceanengineCustomerRechargeDetailClient;
    private final AccountOceanengineCustomerRechargeDetailService accountOceanengineCustomerRechargeDetailService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCustomerRechargeDetailResultVo>> page(@RequestBody @Valid AccountOceanengineCustomerRechargeDetailPageParam param) {
        Rsp<PageResult<com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult>> pageRsp =
                accountOceanengineCustomerRechargeDetailClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<AccountOceanengineCustomerRechargeDetailResultVo> voPageResult =
                PageUtil.convert(pageRsp.getData(), AccountOceanengineCustomerRechargeDetailResultVo.class);
        if (voPageResult.getRecords() != null && !voPageResult.getRecords().isEmpty()) {
            accountOceanengineCustomerRechargeDetailService.fillDetailInfo(voPageResult.getRecords());
        }
        return RspHd.data(voPageResult);
    }


    @Operation(summary = "下载")
    @PostMapping("/download")
    public Rsp<Void> download(@RequestBody @Valid AccountOceanengineCustomerRechargeDetailListParam param) {
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(AccountOceanengineContentDownloadConsumer.ACCOUNT_OCEANENGINE_CUSTOMER_RECHARGE_DETAIL);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

    @Operation(summary = "根据title前缀自助充值")
    @PostMapping("/selfRechargeByTitle")
    public Rsp<Void> selfRechargeByTitle(@RequestBody @Valid AccountOceanengineCustomerSelfRechargeByTitleParam param) {
        return accountOceanengineCustomerRechargeDetailClient.selfRechargeByTitle(param);
    }





}
